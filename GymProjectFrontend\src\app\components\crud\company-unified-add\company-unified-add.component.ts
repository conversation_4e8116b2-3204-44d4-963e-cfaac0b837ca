import { Component, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { CompanyService } from '../../../services/company.service';
import { CompanyadressService } from '../../../services/companyadress.service';
import { CompanyUserService } from '../../../services/company-user.service';
import { UserCompanyService } from '../../../services/usercompany.service';
import { CityService } from '../../../services/city.service';
import { TownService } from '../../../services/town.service';
import { AuthService } from '../../../services/auth.service';
import { UserService } from '../../../services/user-service.service';
import { City } from '../../../models/city';
import { Town } from '../../../models/town';
import { Company } from '../../../models/company';
import { CompanyAdress } from '../../../models/companyAdress';
import { CompanyUser } from '../../../models/companyUser';
import { UserCompany } from '../../../models/usercompany';
import { CompanyAdressDetailDto } from '../../../models/companyAdressDetailDto';
import { CompanyUserDetail } from '../../../models/companyUserDetails';
import { UserCompanyDetail } from '../../../models/userCompanyDetailDto';
import { User } from '../../../models/user';
import { faTrashAlt, faEdit } from '@fortawesome/free-solid-svg-icons';
import { Observable } from 'rxjs';
import { startWith, map } from 'rxjs/operators';
import { MatDialog } from '@angular/material/dialog';

@Component({
  selector: 'app-company-unified-add',
  templateUrl: './company-unified-add.component.html',
  styleUrls: ['./company-unified-add.component.css'],
  standalone: false
})
export class CompanyUnifiedAddComponent implements OnInit {
  unifiedForm: FormGroup;
  cities: City[] = [];
  towns: Town[] = [];
  salons: any[] = []; // Birleştirilmiş salon listesi
  filteredSalons: any[] = []; // Filtrelenmiş salon listesi
  isSubmitting: boolean = false;
  faTrashAlt = faTrashAlt;
  faEdit = faEdit;
  searchTerm: string = '';
  sortColumn: string = 'companyName';
  sortDirection: string = 'asc';

  filteredCities: Observable<City[]>;
  filteredTowns: Observable<Town[]>;
  users: User[] = [];
  filteredUsers: Observable<User[]>;

  constructor(
    private formBuilder: FormBuilder,
    private companyService: CompanyService,
    private companyAdressService: CompanyadressService,
    private companyUserService: CompanyUserService,
    private userCompanyService: UserCompanyService,
    private cityService: CityService,
    private townService: TownService,
    private toastrService: ToastrService,
    private dialog: MatDialog,
    private authService: AuthService,
    private userService: UserService
  ) {}

  ngOnInit(): void {
    this.createUnifiedForm();
    this.getCities();
    this.getTowns();
    this.getSalons();
    this.getUsers();

    this.filteredCities = this.unifiedForm.get('city')!.valueChanges.pipe(
      startWith(''),
      map(value => typeof value === 'string' ? value : value?.cityName),
      map(name => name ? this._filterCities(name) : this.cities.slice())
    );

    this.filteredTowns = this.unifiedForm.get('town')!.valueChanges.pipe(
      startWith(''),
      map(value => typeof value === 'string' ? value : value?.townName),
      map(name => name ? this._filterTowns(name) : this.towns.slice())
    );

    this.filteredUsers = this.unifiedForm.get('ownerEmail')!.valueChanges.pipe(
      startWith(''),
      map(value => typeof value === 'string' ? value : value?.email),
      map(email => email ? this._filterUsers(email) : this.users.slice())
    );
  }

  createUnifiedForm() {
    this.unifiedForm = this.formBuilder.group({
      // Şirket bilgileri
      companyName: ['', Validators.required],
      companyPhone: ['', Validators.required],
      
      // Adres bilgileri
      city: ['', Validators.required],
      town: ['', Validators.required],
      address: ['', Validators.required],
      
      // Şirket sahibi bilgileri
      ownerName: ['', Validators.required],
      ownerPhone: ['', Validators.required],
      ownerEmail: ['', Validators.required] // Removed email validator since we're using an object now
    });
  }

  // Hata durumunda şirketi temizle
  private cleanupCompany(companyId: number): void {
    this.companyService.delete(companyId).subscribe(
      () => {
        console.log(`Hata nedeniyle şirket silindi (ID: ${companyId})`);
      },
      (error) => {
        console.error(`Şirket temizleme hatası (ID: ${companyId}):`, error);
      }
    );
  }

  // Hata durumunda şirket adresini temizle
  private cleanupCompanyAddress(addressId: number): void {
    this.companyAdressService.delete(addressId).subscribe(
      () => {
        console.log(`Hata nedeniyle şirket adresi silindi (ID: ${addressId})`);
      },
      (error) => {
        console.error(`Şirket adresi temizleme hatası (ID: ${addressId}):`, error);
      }
    );
  }

  // Hata durumunda şirket sahibini temizle
  private cleanupCompanyUser(ownerId: number): void {
    this.companyUserService.delete(ownerId).subscribe(
      () => {
        console.log(`Hata nedeniyle şirket sahibi silindi (ID: ${ownerId})`);
      },
      (error) => {
        console.error(`Şirket sahibi temizleme hatası (ID: ${ownerId}):`, error);
      }
    );
  }

  // Tüm verileri tek seferde doğrula
  private validateFormData(): boolean {
    // Kullanıcı seçimini doğrula
    const selectedUser = this.unifiedForm.get('ownerEmail')!.value;
    if (!selectedUser || !selectedUser.email || !selectedUser.userID) {
      this.toastrService.error('Lütfen geçerli bir kullanıcı e-postası seçin', 'Hata');
      return false;
    }

    // Şehir ve ilçe seçimini doğrula
    const city = this.unifiedForm.get('city')!.value;
    const town = this.unifiedForm.get('town')!.value;
    if (!city || !city.cityID || !town || !town.townID) {
      this.toastrService.error('Lütfen geçerli bir şehir ve ilçe seçin', 'Hata');
      return false;
    }

    return true;
  }

  add() {
    if (!this.unifiedForm.valid) {
      this.toastrService.error('Formu eksiksiz doldurunuz', 'Hata');
      return;
    }

    // Tüm verileri önceden doğrula
    if (!this.validateFormData()) {
      return;
    }

    this.isSubmitting = true;
    
    // Kullanıcı bilgisini al
    const selectedUser = this.unifiedForm.get('ownerEmail')!.value;
    
    // 1. Önce şirketi ekle
    const companyModel = {
      companyName: this.unifiedForm.get('companyName')!.value,
      phoneNumber: this.unifiedForm.get('companyPhone')!.value,
      isActive: true
    } as Company;
    
    this.companyService.add(companyModel).subscribe(
      (companyResponse) => {
        // Şirket başarıyla eklendi
        
        // Şirket ID'sini al
        this.companyService.getCompanies().subscribe(
          (companiesResponse) => {
            const companies = companiesResponse.data;
            const addedCompany = companies.find(c => c.companyName === companyModel.companyName);
            
            if (addedCompany) {
              const companyId = addedCompany.companyID;
              
              // 2. Şirket adresini ekle
              const addressModel = {
                companyID: companyId,
                cityID: this.unifiedForm.get('city')!.value.cityID,
                townID: this.unifiedForm.get('town')!.value.townID,
                adress: this.unifiedForm.get('address')!.value
              } as CompanyAdress;
              
              this.companyAdressService.add(addressModel).subscribe(
                (addressResponse) => {
                  // Adres başarıyla eklendi
                  
                  // Adres ID'sini al
                  this.companyAdressService.getCompanyAdressesDetails().subscribe(
                    (addressesResponse) => {
                      const addresses = addressesResponse.data;
                      const addedAddress = addresses.find(a => 
                        a.companyName === companyModel.companyName && 
                        a.adress === addressModel.adress
                      );
                      
                      if (addedAddress) {
                        const addressId = addedAddress.companyAdressID;
                        
                        // 3. Şirket sahibini ekle
                        const ownerModel = {
                          cityID: this.unifiedForm.get('city')!.value.cityID,
                          townID: this.unifiedForm.get('town')!.value.townID,
                          name: this.unifiedForm.get('ownerName')!.value,
                          phoneNumber: this.unifiedForm.get('ownerPhone')!.value,
                          email: selectedUser.email
                        } as CompanyUser;
                        
                        this.companyUserService.add(ownerModel).subscribe(
                          (ownerResponse) => {
                            // Şirket sahibi başarıyla eklendi
                            
                            // Şirket sahibi ID'sini al
                            this.companyUserService.getCompanyUsers().subscribe(
                              (ownersResponse) => {
                                const owners = ownersResponse.data;
                                const addedOwner = owners.find(o => 
                                  o.name === ownerModel.name && 
                                  o.email === ownerModel.email
                                );
                                
                                if (addedOwner) {
                                  const ownerId = addedOwner.companyUserID;

                                  // 4. Şirket ve şirket sahibini ilişkilendir
                                  const relationModel = {
                                    userID: selectedUser.userID,  // Gerçek User ID'sini kullan
                                    companyId: companyId
                                  } as UserCompany;
                                  
                                  this.userCompanyService.add(relationModel).subscribe(
                                    (relationResponse) => {
                                      // İlişki başarıyla eklendi
                                      this.toastrService.success('Salon başarıyla eklendi', 'Başarılı');
                                      this.resetForm();
                                      this.getSalons();
                                      this.isSubmitting = false;
                                    },
                                    (error) => {
                                      this.toastrService.error('Şirket ve şirket sahibi ilişkilendirilemedi', 'Hata');
                                      console.error('İlişkilendirme hatası:', error);
                                      
                                      // Temizleme işlemleri
                                      this.cleanupCompanyUser(ownerId);
                                      this.cleanupCompanyAddress(addressId);
                                      this.cleanupCompany(companyId);
                                      
                                      this.isSubmitting = false;
                                    }
                                  );
                                } else {
                                  this.toastrService.error('Eklenen şirket sahibi bulunamadı', 'Hata');
                                  
                                  // Temizleme işlemleri
                                  this.cleanupCompanyAddress(addressId);
                                  this.cleanupCompany(companyId);
                                  
                                  this.isSubmitting = false;
                                }
                              },
                              (error) => {
                                this.toastrService.error('Şirket sahipleri yüklenemedi', 'Hata');
                                console.error('Şirket sahipleri yükleme hatası:', error);
                                
                                // Temizleme işlemleri
                                this.cleanupCompanyAddress(addressId);
                                this.cleanupCompany(companyId);
                                
                                this.isSubmitting = false;
                              }
                            );
                          },
                          (error) => {
                            this.toastrService.error('Şirket sahibi eklenemedi', 'Hata');
                            console.error('Şirket sahibi ekleme hatası:', error);
                            
                            // Temizleme işlemleri
                            this.cleanupCompanyAddress(addressId);
                            this.cleanupCompany(companyId);
                            
                            this.isSubmitting = false;
                          }
                        );
                      } else {
                        this.toastrService.error('Eklenen şirket adresi bulunamadı', 'Hata');
                        
                        // Temizleme işlemi
                        this.cleanupCompany(companyId);
                        
                        this.isSubmitting = false;
                      }
                    },
                    (error) => {
                      this.toastrService.error('Şirket adresleri yüklenemedi', 'Hata');
                      console.error('Şirket adresleri yükleme hatası:', error);
                      
                      // Temizleme işlemi
                      this.cleanupCompany(companyId);
                      
                      this.isSubmitting = false;
                    }
                  );
                },
                (error) => {
                  this.toastrService.error('Şirket adresi eklenemedi', 'Hata');
                  console.error('Şirket adresi ekleme hatası:', error);
                  
                  // Temizleme işlemi
                  this.cleanupCompany(companyId);
                  
                  this.isSubmitting = false;
                }
              );
            } else {
              this.toastrService.error('Eklenen şirket bulunamadı', 'Hata');
              this.isSubmitting = false;
            }
          },
          (error) => {
            this.toastrService.error('Şirketler yüklenemedi', 'Hata');
            console.error('Şirketler yükleme hatası:', error);
            this.isSubmitting = false;
          }
        );
      },
      (error) => {
        this.toastrService.error('Şirket eklenemedi', 'Hata');
        console.error('Şirket ekleme hatası:', error);
        this.isSubmitting = false;
      }
    );
  }

  getSalons() {
    this.isSubmitting = true;
    
    // Şirket-şirket sahibi ilişkilerini al
    this.userCompanyService.getUserCompanyDetails().subscribe(
      (response) => {
        // UserCompanyDetail modelinde companyId ve userId alanları yok, bu yüzden kendi özel salon modelimizi oluşturuyoruz
        this.salons = [];
        
        // Her bir şirket için salon bilgisi oluştur
        response.data.forEach(uc => {
          // Salon bilgisini oluştur
          const salon = {
            companyName: uc.companyName,
            ownerName: uc.companyUserName,
            userCompanyId: uc.userCompanyId,
            // Diğer bilgiler daha sonra doldurulacak
          };
          
          this.salons.push(salon);
        });
        
        // Şirket adreslerini al ve salon bilgilerine ekle
        this.companyAdressService.getCompanyAdressesDetails().subscribe(
          (addressResponse) => {
            const addresses = addressResponse.data;
            
            this.salons.forEach(salon => {
              // CompanyAdressDetailDto'da companyID alanı olmadığı için companyName ile eşleştiriyoruz
              const address = addresses.find(a => a.companyName === salon.companyName);
              if (address) {
                salon.cityName = address.cityName;
                salon.townName = address.townName;
                salon.address = address.adress;
                salon.companyAdressID = address.companyAdressID;
              }
            });
            
            // Şirketleri al ve salon bilgilerine ekle
            this.companyService.getCompanies().subscribe(
              (companiesResponse) => {
                const companies = companiesResponse.data;
                
                this.salons.forEach(salon => {
                  const company = companies.find(c => c.companyName === salon.companyName);
                  if (company) {
                    salon.companyId = company.companyID;
                  }
                });
                
                // Şirket sahiplerinin detaylarını al ve salon bilgilerine ekle
                this.companyUserService.getCompanyUserDetails().subscribe(
                  (ownerResponse) => {
                    const owners = ownerResponse.data;
                    
                    this.salons.forEach(salon => {
                      const owner = owners.find(o => o.companyUserName === salon.ownerName);
                      if (owner) {
                        salon.ownerId = owner.companyUserId;
                        salon.ownerPhone = owner.companyUserPhoneNumber;
                        salon.ownerEmail = owner.companyUserEmail;
                      }
                    });
                    
                    // Filtrelenmiş salonları başlat
                    this.filteredSalons = [...this.salons];
                    // Varsayılan sıralama uygula
                    this.sortSalons(this.sortColumn);
                    
                    this.isSubmitting = false;
                  },
                  (error) => {
                    console.error('Şirket sahipleri yükleme hatası:', error);
                    this.isSubmitting = false;
                  }
                );
              },
              (error) => {
                console.error('Şirketler yükleme hatası:', error);
                this.isSubmitting = false;
              }
            );
          },
          (error) => {
            console.error('Şirket adresleri yükleme hatası:', error);
            this.isSubmitting = false;
          }
        );
      },
      (error) => {
        console.error('Şirket-şirket sahibi ilişkileri yükleme hatası:', error);
        this.isSubmitting = false;
      }
    );
  }

  // Salonları filtrele
  filterSalons(): void {
    if (!this.searchTerm.trim()) {
      this.filteredSalons = [...this.salons];
    } else {
      const term = this.searchTerm.toLowerCase().trim();
      this.filteredSalons = this.salons.filter(salon => 
        salon.companyName?.toLowerCase().includes(term) || 
        salon.ownerName?.toLowerCase().includes(term) || 
        salon.cityName?.toLowerCase().includes(term) || 
        salon.townName?.toLowerCase().includes(term) ||
        salon.ownerPhone?.toLowerCase().includes(term) ||
        salon.ownerEmail?.toLowerCase().includes(term)
      );
    }
    // Mevcut sıralamayı koru
    this.sortSalons(this.sortColumn);
  }

  // Salonları sırala
  sortSalons(column: string): void {
    this.sortColumn = column;
    this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
    
    this.filteredSalons.sort((a: any, b: any) => {
      let aValue = a[column];
      let bValue = b[column];
      
      // Null veya undefined değerleri kontrol et
      if (aValue === null || aValue === undefined) aValue = '';
      if (bValue === null || bValue === undefined) bValue = '';
      
      // String değerleri küçük harfe çevir
      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }
      
      if (aValue < bValue) {
        return this.sortDirection === 'asc' ? -1 : 1;
      }
      if (aValue > bValue) {
        return this.sortDirection === 'asc' ? 1 : -1;
      }
      return 0;
    });
  }

  // Benzersiz şehir sayısını al
  getUniqueCityCount(): number {
    if (this.salons.length === 0) return 0;
    
    const uniqueCities = new Set(this.salons
      .filter(salon => salon.cityName) // undefined veya null olanları filtrele
      .map(salon => salon.cityName));
    
    return uniqueCities.size;
  }

  getCities() {
    this.cityService.getCities().subscribe((response) => {
      this.cities = response.data;
    });
  }

  getTowns() {
    this.townService.getTowns().subscribe((response) => {
      this.towns = response.data;
    });
  }

  displayCity(city: City): string {
    return city && city.cityName ? city.cityName : '';
  }

  displayTown(town: Town): string {
    return town && town.townName ? town.townName : '';
  }

  private _filterCities(name: string): City[] {
    const filterValue = name.toLowerCase();
    return this.cities.filter(city => city.cityName.toLowerCase().includes(filterValue));
  }

  private _filterTowns(name: string): Town[] {
    const filterValue = name.toLowerCase();
    return this.towns.filter(town => town.townName.toLowerCase().includes(filterValue));
  }

  private _filterUsers(email: string): User[] {
    if (!email) return this.users.slice();
    
    const filterValue = email.toLowerCase();
    return this.users.filter(user => user.email && user.email.toLowerCase().includes(filterValue));
  }

  displayUser(user: User): string {
    return user && user.email ? user.email : '';
  }

  getUsers() {
    this.userService.getAll().subscribe((response) => {
      this.users = response.data;
    });
  }

  resetForm() {
    this.unifiedForm.reset({
      companyName: '',
      companyPhone: '',
      city: '',
      town: '',
      address: '',
      ownerName: '',
      ownerPhone: '',
      ownerEmail: null // Reset to null instead of empty string for object value
    });
  }

  // Ensure this method is properly defined
  deleteSalon(salon: any): void {
    if (confirm("Bu salonu silmek istediğinizden emin misiniz? Bu işlem şirket, adres ve şirket sahibi ilişkilerini silecektir.")) {
      this.isSubmitting = true;
      
      // Önce şirket-şirket sahibi ilişkisini sil
      this.userCompanyService.delete(salon.userCompanyId).subscribe(
        () => {
          this.toastrService.success('Şirket-şirket sahibi ilişkisi silindi', 'Başarılı');
          
          // Şirket adresini sil
          if (salon.companyAdressID) {
            this.companyAdressService.delete(salon.companyAdressID).subscribe(
              () => {
                this.toastrService.success('Şirket adresi silindi', 'Başarılı');
                
                // Şirketi sil
                this.companyService.delete(salon.companyId).subscribe(
                  () => {
                    this.toastrService.success('Salon başarıyla silindi', 'Başarılı');
                    this.getSalons();
                  },
                  (error) => {
                    this.toastrService.error('Şirket silinemedi', 'Hata');
                    console.error('Şirket silme hatası:', error);
                    this.isSubmitting = false;
                  }
                );
              },
              (error) => {
                this.toastrService.error('Şirket adresi silinemedi', 'Hata');
                console.error('Şirket adresi silme hatası:', error);
                this.isSubmitting = false;
              }
            );
          } else {
            // Şirket adresi yoksa doğrudan şirketi sil
            this.companyService.delete(salon.companyId).subscribe(
              () => {
                this.toastrService.success('Salon başarıyla silindi', 'Başarılı');
                this.getSalons();
              },
              (error) => {
                this.toastrService.error('Şirket silinemedi', 'Hata');
                console.error('Şirket silme hatası:', error);
                this.isSubmitting = false;
              }
            );
          }
        },
        (error) => {
          this.toastrService.error('Şirket-şirket sahibi ilişkisi silinemedi', 'Hata');
          console.error('İlişki silme hatası:', error);
          this.isSubmitting = false;
        }
      );
    }
  }
}
